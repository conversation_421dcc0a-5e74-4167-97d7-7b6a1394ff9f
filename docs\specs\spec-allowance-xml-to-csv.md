# 產品需求文件 (PRD)：B0401 電子折讓單 XML ➜ CSV 轉換器

**版本**：1.0  **最後更新**：2025‑07‑05
**作者**：Roo

---

## 1. 目標 (Goal)

將符合財政部電子發票整合服務平台 B0401 v3.1 格式之多個 XML 檔案 (電子折讓單)，批次轉換為一個符合 BTB210W 規範的「H / M / D 三段式」CSV 檔案，供後續匯入 ERP / BI 系統。

---

## 2. 整體範圍 (Scope)

-   **In‑Scope**
    -   解析 B0401 v3.1 XML
    -   依規則輸出單一 CSV
    -   產生唯一的 H 記錄
    -   CLI  工具 (無 GUI)
-   **Out‑of‑Scope**
    -   B0401 以外的格式 (如 A0401 發票)
    -   圖形化使用者介面
    -   直接寫入資料庫
    -   即時線上轉換服務

---

## 3. 典型流程

1.  使用者透過終端機下達指令：

    ```bash
    python allowance_xml_to_csv.py --input ./input/allowance --output ./output/allowance \
            --uploader-phone "02-12345678" --skip-invalid
    ```

2.  程式掃描 `--input` 目錄下所有 `*.xml` 檔案。
3.  逐一解析並驗證 XML 檔案：
    -   確認符合 B0401 的基本結構。
    -   所有檔案中的賣方統編 (`/Allowance/Main/Seller/Identifier`) 必須一致。
4.  依據折讓單日期 (升冪)、再依折讓單號碼 (升冪) 進行排序。
5.  產生 CSV 內容：
    -   **1 筆 H (Header)** 記錄 – 取自第一份有效檔案的賣方資訊。
    -   **n 筆 M (Master)** 記錄 – 每張折讓單產生 1 筆。
    -   **m 筆 D (Detail)** 記錄 – 每張折讓單中的每個 `ProductItem` 產生 1 筆。
6.  將三種記錄依 H→M→D 的順序寫入指定的 CSV 檔案。
7.  輸出檔名應具備可識別性，例如：`B0401_Allowance_[日期]_[時間].csv`，並使用 UTF‑8 編碼。

---

## 4. 功能需求 (Functional Requirements)

| 代號      | 描述                                                                                          |
| --------- | --------------------------------------------------------------------------------------------- |
| **FR‑01** | 程式必須能接收 `--input` 和 `--output` CLI 參數來指定來源與目標目錄。                         |
| **FR‑02** | 支援 `--uploader-phone`, `--encoding`, `--skip-invalid`, `--log-level` 等輔助參數。           |
| **FR‑03** | 若偵測到不同 XML 檔案的賣方統編不一致，程式應立即終止並回傳錯誤碼。                           |
| **FR‑04** | 產出的 CSV 檔案必須嚴格符合 H/M/D 三段式結構，且欄位數量正確。                                |
| **FR‑05** | 當 `--skip-invalid` 啟用時，對於格式錯誤或無法解析的 XML 檔案，應予以略過並在日誌中記錄警告。 |
| **FR‑06** | 日期格式需從 XML 的 `YYYYMMDD` 轉換為 CSV 的 `YYYY/MM/DD`。                                   |

---

## 5. 資料對應 (Data Mapping)

### 5.1 H Record – 上傳者資訊 (唯一)

| #   | CSV 欄位       | 來源 XPath / 固定值                 | 型別 | 備註                      |
| --- | -------------- | ----------------------------------- | ---- | ------------------------- |
| 1   | 代碼           | 固定值 `H`                          | str  |                           |
| 2   | 上傳者統一編號 | `/Allowance/Main/Seller/Identifier` | str  | 對應 XML 中的「賣方」     |
| 3   | 上傳者公司名稱 | `/Allowance/Main/Seller/Name`       | str  |                           |
| 4   | 上傳者公司地址 | `/Allowance/Main/Seller/Address`    | str  |                           |
| 5   | 上傳者公司電話 | CLI 參數 `--uploader-phone`         | str  | 若未提供則預設 03-3631230 |
| 6   | 發票作業類別   | 固定值 `CRE_ALW`                    | str  |                           |

### 5.2 M Record – 折讓單主檔

| #   | CSV 欄位                 | 來源 XPath / 固定值                 | 型別 | 備註                    |
| --- | ------------------------ | ----------------------------------- | ---- | ----------------------- |
| 1   | 代碼                     | 固定值 `M`                          | str  |                         |
| 2   | 折讓證明單號碼           | `/Allowance/Main/AllowanceNumber`   | str  |                         |
| 3   | 折讓證明單日期           | `/Allowance/Main/AllowanceDate`     | str  | 格式需轉為 `YYYY/MM/DD` |
| 4   | 買方統一編號             | `/Allowance/Main/Buyer/Identifier`  | str  |                         |
| 5   | 買方公司名稱             | `/Allowance/Main/Buyer/Name`        | str  |                         |
| 6   | 買方公司地址             | `/Allowance/Main/Buyer/Address`     | str  |                         |
| 7   | 賣方統一編號             | `/Allowance/Main/Seller/Identifier` | str  |                         |
| 8   | 賣方公司名稱             | `/Allowance/Main/Seller/Name`       | str  |                         |
| 9   | 賣方公司地址             | `/Allowance/Main/Seller/Address`    | str  |                         |
| 10  | 營業稅額合計             | `/Allowance/Amount/TaxAmount`       | int  |                         |
| 11  | 金額(不含稅之進貨額)合計 | `/Allowance/Amount/TotalAmount`     | int  |                         |

### 5.3 D Record – 折讓單明細

| #   | CSV 欄位                 | 來源 XPath                                             | 型別      | 備註                     |
| --- | ------------------------ | ------------------------------------------------------ | --------- | ------------------------ |
| 1   | 代碼                     | 固定值 `D`                                             | str       |                          |
| 2   | 發票品名                 | `/Allowance/Details/ProductItem/OriginalDescription`   | str       |                          |
| 3   | 發票號碼                 | `/Allowance/Details/ProductItem/OriginalInvoiceNumber` | str       | 此為原始發票號碼         |
| 4   | 發票日期                 | `/Allowance/Details/ProductItem/OriginalInvoiceDate`   | str       | 格式需轉為 `YYYY/MM/DD`  |
| 5   | 發票數量                 | `/Allowance/Details/ProductItem/Quantity`              | int/float |                          |
| 6   | 發票單價                 | `/Allowance/Details/ProductItem/UnitPrice`             | float     |                          |
| 7   | 發票金額(不含稅之進貨額) | `/Allowance/Details/ProductItem/Amount`                | int       |                          |
| 8   | 營業稅額                 | `/Allowance/Details/ProductItem/Tax`                   | int       |                          |
| 9   | 課稅別                   | `/Allowance/Details/ProductItem/TaxType`               | int       | 1:應稅, 2:零稅率, 3:免稅 |

---

## 6. CLI 參數一覽

| 參數               | 必填 | 預設        | 說明                                            |
| ------------------ | ---- | ----------- | ----------------------------------------------- |
| `--input, -i`      | 是   | N/A         | B0401 XML 來源目錄                              |
| `--output, -o`     | 否   | `./`        | CSV 輸出目錄                                    |
| `--uploader-phone` | 否   | 空字串      | H 記錄中的上傳者電話，若未提供則預設 03-3631230 |
| `--encoding`       | 否   | `utf-8-sig` | 輸出 CSV 檔案的文字編碼                         |
| `--skip-invalid`   | 否   | `false`     | 是否跳過無效或格式錯誤的 XML 檔案               |
| `--log-level`      | 否   | `INFO`      | 設定日誌記錄層級 (DEBUG, INFO, WARNING, ERROR)  |
| `--version`        | 否   | –           | 顯示程式版本號並結束                            |

**Exit Codes:**

-   `0`: 成功
-   `1`: 一般錯誤 (如檔案讀取失敗)
-   `2`: 賣方統編不一致

---

## 7. 測試案例 (Test Cases)

| TC ID | 目的                | 測試步驟                                    | 預期結果                                                               |
| ----- | ------------------- | ------------------------------------------- | ---------------------------------------------------------------------- |
| TC-01 | 正常轉換 (單一檔案) | 提供一個有效的 B0401 XML 檔案。             | 成功產生一個 CSV，包含 1xH, 1xM, 1xD 記錄。                            |
| TC-02 | 正常轉換 (多個檔案) | 提供多個賣方統編相同的 B0401 XML。          | 成功產生一個合併的 CSV，記錄數正確。                                   |
| TC-03 | 賣方不一致錯誤      | 提供兩個賣方統編不同的 B0401 XML。          | 程式終止，回傳 exit code 2，並顯示錯誤訊息。                           |
| TC-04 | 處理無效檔案        | 在輸入目錄中放入一個非 XML 或已損毀的檔案。 | 啟用 `--skip-invalid` 時，程式應略過該檔案並完成轉換；日誌中應有警告。 |
| TC-05 | 日期格式驗證        | 檢查輸出 CSV 中的日期欄位。                 | 所有日期格式均為 `YYYY/MM/DD`。                                        |
