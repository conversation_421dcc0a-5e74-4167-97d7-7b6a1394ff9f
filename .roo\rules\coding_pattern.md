# 🔷 軟體架構設計模式與模組劃分原則

本專案採用 **Clean Architecture** 設計原則，目的是建立一套清晰、一致、可維護的專案結構與開發規範，協助團隊在開發上有明確的模組劃分和開發規範。

## Clean Architecture 設計原則

-   **分層責任**：
    -   `Entities`：封裝核心業務邏輯與資料模型
    -   `Use Case`：
        -   執行核心商業邏輯，調用不同的 Repository。
    -   `Repository`：
        -   封裝 檔案操作、SQL ORM、Elasticsearch 或第三方 API 存取邏輯
        -   必須嚴格定義函數 input 和 output 的 type hints。
-   **依賴方向**：
    -   Use Case → Repository
