# 電子發票與折讓單 XML 轉 CSV 工具操作手冊

## 1. 前言

本手冊旨在說明如何使用「電子發票 XML 轉 CSV」與「電子折讓單 XML 轉 CSV」這兩款批次轉換工具。

這兩款工具可以將財政部電子發票整合服務平台的 A0401 (電子發票) 與 B0401 (電子折讓單) 格式的 XML 檔案，快速、批次地轉換為 ERP 或 BI 系統易於處理的 CSV 檔案。

---

## 2. 程式執行檔

工具包含兩個獨立的執行檔，分別用於處理發票和折讓單：

-   `invoice_xml_to_csv.exe`: 用於轉換**電子發票**。
-   `allowance_xml_to_csv.exe`: 用於轉換**電子折讓單**。
-   `input` 資料夾: 專門存放 XML 輸入檔案。

![執行檔](images/user_manual_001.png)

---

## 3. XML 輸入檔案準備

在執行轉換前，請將對應的 XML 檔案放置於指定的資料夾中。

1. 進入 input 資料夾，並創建 allowance 和 invoice 兩個資料夾。
   ![input 資料夾](images/user_manual_002.png)

2. 將**電子折讓單**的 XML 檔案放入 `input/allowance` 資料夾。
   ![折讓單輸入路徑](images/user_manual_003.png)

3. 將**電子發票**的 XML 檔案放入 `input/invoice` 資料夾。
   ![發票輸入路徑](images/user_manual_004.png)

---

## 4. 執行轉換

1.  根據您要轉換的檔案類型，找到對應的執行檔。
2.  直接**雙擊 (Double Click)** `invoice_xml_to_csv.exe` 或 `allowance_xml_to_csv.exe` 來啟動程式。

---

## 5. 執行過程與完成

1.  執行後，會彈出一個命令提示字元視窗，顯示程式正在處理中。
2.  當視窗中出現 `Press any key to exit...` 的訊息時，即表示轉換程式結束。
3.  此時，您可以按鍵盤上**任意一個按鍵** (例如：`Esc` 或 `Enter`) 來關閉此視窗。

![執行完成畫面](images/user_manual_005.png)

---

## 6. 檢視轉換結果

轉換完成後，產生的 CSV 檔案會存放在 `output` 資料夾中。

1.  **電子發票**轉換後的 CSV 檔會位於 `output/invoice` 資料夾下。
    ![發票輸出結果](images/user_manual_006.png)

2.  **電子折讓單**轉換後的 CSV 檔會位於 `output/allowance` 資料夾下。
    ![折讓單輸出結果](images/user_manual_007.png)

現在您可以開啟這些 CSV 檔案，進行後續的資料匯入或分析作業。
