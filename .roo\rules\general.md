-   開發環境為 Windows 系統，終端機指令請使用 Git Bash 相容語法
-   規劃專案時須提供使用框架的分析建議，並由用戶選擇最終使用的框架
-   專案文件均在專案根目錄的 docs 資料夾下，其中包含:

    -   架構文件(architectures 資料夾下): 包含專案的資料夾結構、架構設計、模組劃分、模組/函數間相依關係、資料流、業務邏輯、Elasticsearch 資料庫 Index schema 等
    -   開發規格文件(specs 資料夾下): 包含各 feature 的開發規格文件，每個 feature 一個 spec-xxx.md 文件
    -   任務清單(todos 資料夾下): 包含每個 feature 詳細 breakdown 後的任務清單，可先拆分成幾個大任務，再拆分成幾個小任務，每個 feature 一個 todolist-xxx.md 文件。
    -   開發功能報告(reports 資料夾下): 包含每個 feature 的開發報告，包含開發遇到的問題、解決方案、測試結果等，每個 feature 一個 report-xxx.md 文件

-   Co-ordination 模式/協調者/迴旋標模式最後需將所有子任務完成報告記錄在任務報告檔案中(docs/reports/report-xxx.md)
-   架構模式完成後需產生/更新規格文件(docs/specs/spec-xxx.md)以及任務清單(docs/todos/todolist-xxx.md)，規格文件須包含流程圖、循序圖、物件關聯圖如 UML 等相關文件
-   Code 模式需遵循 docs/specs/spec-xxx.md 開發，每次修改程式前都需確認 specs/spec-xxx.md，任務完成後都需更新任務進度( docs/todos/todolist-xxx.md )
-   每個 feature 完成開發時，需撰寫/更新 README.md，內容包含專案描述、檔案目錄結構、使用技術棧、檔案清單，檔案簡短說明、安裝及執行方式
-   ** Important**
    -   每個任務完成後，請在 docs/todos/todolist-xxx.md 文件中在對應的任務前打勾 ✅ 完成
    -   Clarify any questions, and verify the requirements with me before proceed
