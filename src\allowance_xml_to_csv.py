#!/usr/bin/env python3
"""allowance_xml_to_csv.py

CLI tool to convert one or more B0401 v3.1 e-allowance XML files into a single CSV.

Usage:
    python allowance_xml_to_csv.py -i INPUT_DIR -o OUTPUT_DIR [--uploader-phone PHONE]

The script follows the specification in 'docs/specs/spec-allowance-xml-to-csv.md'.
"""

import argparse
import csv
import msvcrt
import os
import sys
import xml.etree.ElementTree as ET
from datetime import datetime
from typing import Any, Dict, List, Optional

__version__ = "0.0.1"

# Namespace for B0401 v3.1 XML
NAMESPACE = {"inv": "urn:GEINV:eInvoiceMessage:B0401:3.1"}

# Column counts for each record type, based on the specification.
H_COLS = 6
M_COLS = 11
D_COLS = 9


def _get_text(elem, xpath: str) -> str:
    """Helper to fetch text from XML with namespace."""
    found = elem.find(xpath, NAMESPACE)
    return found.text.strip() if found is not None and found.text else ""


def _format_date(date_str: str) -> str:
    """Converts YYYYMMDD to YYYY/MM/DD."""
    if not date_str or len(date_str) != 8:
        return ""
    return f"{date_str[:4]}/{date_str[4:6]}/{date_str[6:]}"


def parse_allowance(xml_path: str) -> Optional[Dict[str, Any]]:
    """Parse a single allowance XML and return structured data."""
    try:
        tree = ET.parse(xml_path)
        root = tree.getroot()
    except ET.ParseError:
        print(f"Warning: Could not parse XML file {xml_path}. Skipping.")
        return None

    main = root.find("inv:Main", NAMESPACE)
    if main is None:
        print(f"Warning: <Main> tag not found in {xml_path}. Skipping.")
        return None

    amount = root.find("inv:Amount", NAMESPACE)
    if amount is None:
        print(f"Warning: <Amount> tag not found in {xml_path}. Skipping.")
        return None

    details = root.find("inv:Details", NAMESPACE)
    if details is None:
        print(f"Warning: <Details> tag not found in {xml_path}. Skipping.")
        return None

    seller = main.find("inv:Seller", NAMESPACE)
    if seller is None:
        print(f"Warning: <Seller> tag not found in {xml_path}. Skipping.")
        return None

    buyer = main.find("inv:Buyer", NAMESPACE)
    if buyer is None:
        print(f"Warning: <Buyer> tag not found in {xml_path}. Skipping.")
        return None

    allowance_date_str = _get_text(main, "inv:AllowanceDate")
    allowance_date_obj = datetime.strptime(allowance_date_str, "%Y%m%d")

    return {
        "seller": {
            "Identifier": _get_text(seller, "inv:Identifier"),
            "Name": _get_text(seller, "inv:Name"),
            "Address": _get_text(seller, "inv:Address"),
        },
        "allowance": {
            "AllowanceNumber": _get_text(main, "inv:AllowanceNumber"),
            "AllowanceDate": _format_date(allowance_date_str),
            "AllowanceDateObject": allowance_date_obj,
            "BuyerID": _get_text(buyer, "inv:Identifier"),
            "BuyerName": _get_text(buyer, "inv:Name"),
            "BuyerAddress": _get_text(buyer, "inv:Address"),
            "SellerID": _get_text(seller, "inv:Identifier"),
            "SellerName": _get_text(seller, "inv:Name"),
            "SellerAddress": _get_text(seller, "inv:Address"),
            "TaxAmount": _get_text(amount, "inv:TaxAmount"),
            "TotalAmount": _get_text(amount, "inv:TotalAmount"),
        },
        "items": [
            {
                "OriginalDescription": _get_text(item, "inv:OriginalDescription"),
                "OriginalInvoiceNumber": _get_text(item, "inv:OriginalInvoiceNumber"),
                "OriginalInvoiceDate": _format_date(
                    _get_text(item, "inv:OriginalInvoiceDate")
                ),
                "Quantity": _get_text(item, "inv:Quantity"),
                "UnitPrice": _get_text(item, "inv:UnitPrice"),
                "Amount": _get_text(item, "inv:Amount"),
                "Tax": _get_text(item, "inv:Tax"),
                "TaxType": _get_text(item, "inv:TaxType"),
            }
            for item in details.findall("inv:ProductItem", NAMESPACE)
        ],
    }


def build_h_record(seller: dict, phone: str = "") -> List[str]:
    """Builds the H (Header) record."""
    row = [""] * H_COLS
    row[0] = "H"
    row[1] = seller["Identifier"]
    row[2] = seller["Name"]
    row[3] = seller["Address"]
    row[4] = phone if phone else "03-3631230"
    row[5] = "CRE_ALW"
    return row


def build_m_record(alw: dict) -> List[str]:
    """Builds the M (Master) record for an allowance."""
    row = [""] * M_COLS
    row[0] = "M"
    row[1] = alw["AllowanceNumber"]
    row[2] = alw["AllowanceDate"]
    row[3] = alw["BuyerID"]
    row[4] = alw["BuyerName"]
    row[5] = alw["BuyerAddress"]
    row[6] = alw["SellerID"]
    row[7] = alw["SellerName"]
    row[8] = alw["SellerAddress"]
    row[9] = alw["TaxAmount"]
    row[10] = alw["TotalAmount"]
    return row


def build_d_record(item: dict) -> List[str]:
    """Builds the D (Detail) record for an allowance item."""
    row = [""] * D_COLS
    row[0] = "D"
    row[1] = item["OriginalDescription"]
    row[2] = item["OriginalInvoiceNumber"]
    row[3] = item["OriginalInvoiceDate"]
    row[4] = item["Quantity"]
    # row[5] = item["UnitPrice"]
    row[5] = str(round(float(item["Amount"]) / float(item["Quantity"]), 2))
    row[6] = item["Amount"]
    row[7] = item["Tax"]
    row[8] = item["TaxType"]
    return row


def write_csv(rows: List[List[str]], out_path: str, encoding: str = "utf-8-sig"):
    """Writes the provided rows to a CSV file."""
    with open(out_path, "w", newline="", encoding=encoding) as f:
        writer = csv.writer(f)
        writer.writerows(rows)


def get_base_path():
    """Gets the base path, accounting for PyInstaller."""
    if getattr(sys, "frozen", False):
        # Running as a bundle
        base_path = os.path.dirname(sys.executable)
    else:
        # Running as a script
        base_path = os.path.dirname(os.path.abspath(__file__))
    return base_path


def main():
    """Main function to execute the script."""
    print(f"allowance_xml_to_csv.py v{__version__}")
    base_path = get_base_path()
    # print(f"Base path determined to be: {base_path}")

    parser = argparse.ArgumentParser(
        description="Convert B0401 XML files to a merged CSV.",
        formatter_class=argparse.RawTextHelpFormatter,
    )
    parser.add_argument(
        "-i",
        "--input-dir",
        default=os.path.join(base_path, "input", "allowance"),
        help="Folder with B0401 XML files.",
    )
    parser.add_argument(
        "-o",
        "--output-dir",
        default=os.path.join(base_path, "output", "allowance"),
        help="Folder to write the output CSV file.",
    )
    parser.add_argument(
        "--uploader-phone",
        default="03-3631230",
        help="Seller phone number for the H record.",
    )
    parser.add_argument(
        "--skip-invalid",
        action="store_true",
        help="Skip invalid or unparseable XML files.",
    )
    args = parser.parse_args()

    # Create directories if they don't exist
    os.makedirs(args.input_dir, exist_ok=True)
    os.makedirs(args.output_dir, exist_ok=True)

    xml_paths = [
        os.path.join(args.input_dir, f)
        for f in os.listdir(args.input_dir)
        if f.lower().endswith(".xml")
    ]
    print(f"Found {len(xml_paths)} XML files in {args.input_dir}")
    if not xml_paths:
        print("No XML files found in the input directory.")
        print("Press any key to exit...")
        msvcrt.getch()
        sys.exit()

    allowances = []
    ref_seller_id = None
    ref_seller_info = None

    for path in xml_paths:
        data = parse_allowance(path)
        if data is None:
            if args.skip_invalid:
                continue
            else:
                print(f"Error processing file {path}. Use --skip-invalid to ignore.")
                print("Press any key to exit...")
                msvcrt.getch()
                sys.exit(1)

        current_seller_id = data["seller"]["Identifier"]
        if ref_seller_id is None:
            ref_seller_id = current_seller_id
            ref_seller_info = data["seller"]
        elif current_seller_id != ref_seller_id:
            print(
                f"Error: Seller identifier mismatch. "
                f"Expected {ref_seller_id}, but found {current_seller_id} in {os.path.basename(path)}. "
            )
            print("Press any key to exit...")
            msvcrt.getch()
            sys.exit(2)

        allowances.append(data)

    if not allowances:
        print("No valid allowance data could be parsed from the XML files.")
        print("Press any key to exit...")
        msvcrt.getch()
        sys.exit(3)

    # sort allowances by date/time, then by allowance number
    allowances.sort(
        key=lambda x: (
            x["allowance"]["AllowanceDateObject"],
            int(x["allowance"]["AllowanceNumber"][2:]),
        )
    )

    csv_rows = []
    csv_rows.append(build_h_record(ref_seller_info, args.uploader_phone))

    for alw_data in allowances:
        csv_rows.append(build_m_record(alw_data["allowance"]))
        for item in alw_data["items"]:
            csv_rows.append(build_d_record(item))

    first_no = allowances[0]["allowance"]["AllowanceNumber"]
    last_no = allowances[-1]["allowance"]["AllowanceNumber"]
    out_file = f"B0401_合併_{first_no}～{last_no}.csv"
    out_path = os.path.join(args.output_dir, out_file)

    write_csv(csv_rows, out_path)
    print(f"CSV file successfully generated: {out_path}")
    print("Press any key to exit...")
    msvcrt.getch()


if __name__ == "__main__":
    main()
