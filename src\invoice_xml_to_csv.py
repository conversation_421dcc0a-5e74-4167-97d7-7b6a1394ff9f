#!/usr/bin/env python3
"""xml_to_csv.py

CLI tool to convert one or more A0401 v3.1 e‑invoice XML files into a single CSV.

Usage:
    python xml_to_csv.py -i INPUT_DIR -o OUTPUT_DIR [--seller-phone PHONE]

The script follows the specification in PRD 'XML 轉 CSV 轉換器'.
"""

import argparse
import csv
import msvcrt
import os
import sys
import xml.etree.ElementTree as ET
from datetime import datetime
from typing import Any, Dict, List, Optional

__version__ = "0.0.1"

NAMESPACE = {"inv": "urn:GEINV:eInvoiceMessage:A0401:3.1"}
# Column counts for each record type, based on CSV sample analysis.
H_COLS = 5
M_COLS = 22
D_COLS = 8


def _get_text(elem, xpath: str) -> str:
    """Helper to fetch text from XML with namespace."""
    found = elem.find(xpath, NAMESPACE)
    return found.text.strip() if found is not None and found.text else ""


def parse_invoice(xml_path: str) -> Optional[Dict[str, Any]]:
    """Parse a single invoice XML and return structured data."""
    tree = ET.parse(xml_path)
    root = tree.getroot()

    main = root.find("inv:Main", NAMESPACE)
    if main is None:
        print(f"Warning: <Main> tag not found in {xml_path}. Skipping.")
        return None

    amount = root.find("inv:Amount", NAMESPACE)
    if amount is None:
        print(f"Warning: <Amount> tag not found in {xml_path}. Skipping.")
        return None

    details = root.find("inv:Details", NAMESPACE)
    if details is None:
        print(f"Warning: <Details> tag not found in {xml_path}. Skipping.")
        return None

    seller = main.find("inv:Seller", NAMESPACE)
    if seller is None:
        print(f"Warning: <Seller> tag not found in {xml_path}. Skipping.")
        return None

    buyer = main.find("inv:Buyer", NAMESPACE)
    if buyer is None:
        print(f"Warning: <Buyer> tag not found in {xml_path}. Skipping.")
        return None

    invoice_number = _get_text(main, "inv:InvoiceNumber")
    invoice_date = _get_text(main, "inv:InvoiceDate")  # YYYYMMDD
    invoice_time = _get_text(main, "inv:InvoiceTime")

    # Format date/time to YYYY/MM/DD HH:MM:SS
    dt = datetime.strptime(invoice_date + invoice_time, "%Y%m%d%H:%M:%S")
    formatted_dt = dt.strftime("%Y/%m/%d %H:%M:%S")

    # Tax rate as integer percent
    raw_tax_rate = _get_text(amount, "inv:TaxRate")
    tax_rate = int(round(float(raw_tax_rate) * 100)) if raw_tax_rate else ""

    return {
        "seller": {
            "Identifier": _get_text(seller, "inv:Identifier"),
            "Name": _get_text(seller, "inv:Name"),
            "Address": _get_text(seller, "inv:Address"),
        },
        "invoice": {
            "InvoiceNumber": invoice_number,
            "DateTime": formatted_dt,
            "DateTimeObject": dt,
            "InvoiceType": _get_text(main, "inv:InvoiceType"),
            "BuyerID": _get_text(buyer, "inv:Identifier"),
            "BuyerName": _get_text(buyer, "inv:Name"),
            "BuyerAddress": _get_text(buyer, "inv:Address"),
            "TaxType": _get_text(amount, "inv:TaxType"),
            "TaxRate": str(tax_rate),
            "SalesAmount": _get_text(amount, "inv:SalesAmount"),
            "TaxAmount": _get_text(amount, "inv:TaxAmount"),
            "TotalAmount": _get_text(amount, "inv:TotalAmount"),
        },
        "items": [
            {
                "Description": _get_text(item, "inv:Description"),
                "Quantity": _get_text(item, "inv:Quantity"),
                "UnitPrice": _get_text(item, "inv:UnitPrice"),
                "Amount": _get_text(item, "inv:Amount"),
            }
            for item in details.findall("inv:ProductItem", NAMESPACE)
        ],
    }


def build_h_record(seller: dict, phone: str = "") -> List[str]:
    row = [""] * H_COLS
    row[0] = "H"
    row[1] = seller["Identifier"]
    row[2] = seller["Name"]
    row[3] = seller["Address"]
    row[4] = phone if phone else "03-3631230"
    return row


def build_m_record(inv: dict) -> List[str]:
    row = [""] * M_COLS
    row[0] = "M"
    row[1] = inv["InvoiceNumber"]
    row[2] = inv["DateTime"]
    row[3] = inv["InvoiceType"]
    row[4] = inv["BuyerID"]
    row[5] = inv["BuyerName"]
    row[6] = inv["BuyerAddress"]
    row[7] = inv["TaxType"]
    row[8] = inv["TaxRate"]
    row[9] = inv["SalesAmount"]
    row[10] = inv["TaxAmount"]
    row[11] = inv["TotalAmount"]

    # For zero-rated invoices (TaxType=2, TaxRate=0), add extra fields
    # for customs clearance, based on a specific user requirement.
    if inv["TaxType"] == "2" and inv["TaxRate"] == "0":
        row[12] = "1"
        row[13] = "1"  # Column 14
        # Column 15 is intentionally blank.
        row[15] = "74"  # Column 16

    return row


def build_d_record(item: dict) -> List[str]:
    row = [""] * D_COLS
    row[0] = "D"
    row[1] = item["Description"]
    row[2] = item["Quantity"]
    row[3] = item["UnitPrice"]
    row[4] = item["Amount"]
    return row


def write_csv(rows: List[List[str]], out_path: str):
    with open(out_path, "w", newline="", encoding="utf-8-sig") as f:
        writer = csv.writer(f)
        writer.writerows(rows)


def get_base_path():
    """Gets the base path, accounting for PyInstaller."""
    if getattr(sys, "frozen", False):
        # Running as a bundle
        base_path = os.path.dirname(sys.executable)
    else:
        # Running as a script
        base_path = os.path.dirname(os.path.abspath(__file__))
    return base_path


def main():
    print(f"invoice_xml_to_csv.py v{__version__}")
    base_path = get_base_path()
    # print(f"Base path determined to be: {base_path}")

    parser = argparse.ArgumentParser(
        description="Convert A0401 XML files to a merged CSV."
    )
    parser.add_argument(
        "-i",
        "--input-dir",
        default=os.path.join(base_path, "input", "invoice"),
        help="Folder with A0401 XML files (default: input)",
    )
    parser.add_argument(
        "-o",
        "--output-dir",
        default=os.path.join(base_path, "output", "invoice"),
        help="Folder to write CSV (default: output)",
    )
    parser.add_argument(
        "--seller-phone", default="", help="Seller phone number (optional)"
    )
    args = parser.parse_args()

    # Create directories if they don't exist
    os.makedirs(args.input_dir, exist_ok=True)
    os.makedirs(args.output_dir, exist_ok=True)

    xml_paths = [
        os.path.join(args.input_dir, f)
        for f in os.listdir(args.input_dir)
        if f.lower().endswith(".xml")
    ]
    print(f"Found {len(xml_paths)} XML files in {args.input_dir}")
    if not xml_paths:
        print("No XML files found in input directory")
        print("Press any key to exit...")
        msvcrt.getch()
        sys.exit()

    invoices = []
    ref_seller = None
    for path in xml_paths:
        data = parse_invoice(path)
        if data is None:
            continue

        if ref_seller is None:
            ref_seller = data["seller"]
        elif data["seller"]["Identifier"] != ref_seller["Identifier"]:
            print(f"Seller mismatch in file {path}")
            print("Press any key to exit...")
            msvcrt.getch()
            sys.exit(1)

        invoices.append(data)

    if not invoices:
        print("No valid invoice data found in XML files.")
        print("Press any key to exit...")
        msvcrt.getch()
        sys.exit(2)

    # sort invoices by date/time, then by invoice number
    invoices.sort(
        key=lambda x: (
            x["invoice"]["DateTimeObject"],
            int(x["invoice"]["InvoiceNumber"][2:]),
        )
    )

    rows = []

    rows.append(build_h_record(ref_seller, args.seller_phone))
    for inv in invoices:
        rows.append(build_m_record(inv["invoice"]))
        for item in inv["items"]:
            rows.append(build_d_record(item))

    first_no = invoices[0]["invoice"]["InvoiceNumber"]
    last_no = invoices[-1]["invoice"]["InvoiceNumber"]
    out_file = f"A0401_合併_{first_no}～{last_no}.csv"
    out_path = os.path.join(args.output_dir, out_file)
    write_csv(rows, out_path)
    print("CSV generated:", out_path)
    print("Press any key to exit...")
    msvcrt.getch()


if __name__ == "__main__":
    main()
