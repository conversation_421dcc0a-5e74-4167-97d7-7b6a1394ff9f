# 產品需求文件 (PRD)：A0401 電子發票 XML ➜ CSV 批次轉換器

**版本**：1.2  **最後更新**：2025‑07‑05
**作者**：李明杰

---

## 1. 目標 (Goal)

將符合財政部電子發票整合服務平台 A0401 v3.1 格式之多個 XML 檔案，批次轉換為一個符合「H / M / D 三段式」規格的 CSV 檔，供後續匯入 ERP / BI。

---

## 2. 整體範圍 (Scope)

-   **In‑Scope**
    -   解析 A0401 v3.1 XML
    -   依規則輸出單一 CSV
    -   唯一 H 記錄
    -   CLI  工具 (無 GUI)
-   **Out‑of‑Scope**
    -   A0401 以外格式
    -   圖形介面
    -   寫回 DB
    -   即時線上服務

---

## 3. 典型流程

1. 使用者以 CLI 下達指令：

    ```bash
    xml2csv --input ./invoices --output ./result \
            --seller-phone "03-3631230" --skip-invalid
    ```

2. 程式掃描 `--input` 目錄內的 `*.xml`。
3. 逐檔解析並驗證：

    - XML schema & 基本欄位完整性。
    - 所有檔案 `/Invoice/Main/Seller/Identifier` 必須一致。

4. 依發票日期時間 (升冪)、再依發票號碼 (升冪) 排序。
5. 產生：

    - 1 筆 **H**(Header) – 取第一檔賣方資訊。
    - n 筆 **M**(Master) – 每張發票 1 筆。
    - m 筆 **D**(Detail) – 每個 `ProductItem` 1 筆。

6. 將三種紀錄依 H→M→D 次序寫入 CSV。
7. 輸出檔名：
   `A0401_合併_[首號]～[末號]_V4.csv`，採 UTF‑8 BOM。

---

## 4. 功能需求 (Functional Requirements)

| 代號      | 描述                                                                          |
| --------- | ----------------------------------------------------------------------------- |
| **FR‑01** | 程式能接收 `--input`, `--output` 參數；若缺省則使用工作目錄。                 |
| **FR‑02** | 支援 `--seller-phone`, `--encoding`, `--skip-invalid`, `--log-level` 參數。   |
| **FR‑03** | 若偵測到賣方不一致，程式須結束並回傳 exit code 2。                            |
| **FR‑04** | 處理 1,000 份 XML ≤ 60 s (測試環境：8 vCPU / 4 GB)。                          |
| **FR‑05** | 當 `--skip-invalid` 啟用時，無法解析之檔案須被略過並於 log 告警。             |
| **FR‑06** | 產出 CSV 應符合 H/M/D 各自的欄位數 (H:5, M:23, D:8)；未使用欄位以空字串保留。 |

---

## 5. 資料對應 (Data Mapping)

### 5.1 H Record – 賣方 (唯一)

| #   | CSV 欄位 | 來源 XPath                        | 型別 | 備註     |
| --- | -------- | --------------------------------- | ---- | -------- |
| 1   | 紀錄類型 | 固定值 `H`                        | str  |          |
| 2   | 賣方統編 | `/Invoice/Main/Seller/Identifier` | str  |          |
| 3   | 賣方名稱 | `/Invoice/Main/Seller/Name`       | str  |          |
| 4   | 賣方地址 | `/Invoice/Main/Seller/Address`    | str  |          |
| 5   | 賣方電話 | CLI 參數 `--seller-phone`         | str  | 空值允許 |

### 5.2 M Record – 發票主檔

| #     | CSV 欄位     | XPath                                | 型別 | 備註                                                           |
| ----- | ------------ | ------------------------------------ | ---- | -------------------------------------------------------------- |
| 1     | 紀錄類型     | 固定值 `M`                           | str  |                                                                |
| 2     | 發票號碼     | `/Invoice/Main/InvoiceNumber`        | str  |                                                                |
| 3     | 發票日期時間 | Date + Time                          | str  | `YYYY/MM/DD HH:mm:ss`                                          |
| 4     | 發票類型     | `/Invoice/Main/InvoiceType`          | str  |                                                                |
| 5     | 買方統編     | `/Invoice/Main/Buyer/Identifier`     | str  |                                                                |
| 6     | 買方名稱     | `/Invoice/Main/Buyer/Name`           | str  |                                                                |
| 7     | 買方地址     | `/Invoice/Main/Buyer/Address`        | str  |                                                                |
| 8     | 課稅別       | `/Invoice/Amount/TaxType`            | int  | 1:應稅, 2:零稅率, 3:免稅                                       |
| 9     | 稅率(%)      | `/Invoice/Amount/TaxRate × 100`      | int  | 四捨五入                                                       |
| 10    | 銷售額合計   | `/Invoice/Amount/SalesAmount`        | int  | 未稅                                                           |
| 11    | 稅額         | `/Invoice/Amount/TaxAmount`          | int  |                                                                |
| 12    | 總計         | `/Invoice/Amount/TotalAmount`        | int  | 含稅                                                           |
| 13    | 通關方式註記 | `/Invoice/Main/CustomsClearanceMark` | str  | **零稅率 (課稅別=2) 發票專用**<br>1:非經海關出口, 2:經海關出口 |
| 14    | (保留)       | 固定值 `1`                           | str  | **零稅率 (課稅別=2) 發票專用**                                 |
| 15    | (保留)       | –                                    | –    | 留空                                                           |
| 16    | (保留)       | 固定值 `74`                          | str  | **零稅率 (課稅別=2) 發票專用**                                 |
| 17‑22 | (保留)       | –                                    | –    | 留空                                                           |

### 5.3 D Record – 發票明細

| #   | CSV 欄位 | XPath                     | 型別      | 備註 |
| --- | -------- | ------------------------- | --------- | ---- |
| 1   | 紀錄類型 | 固定值 `D`                | str       |      |
| 2   | 品名     | `ProductItem/Description` | str       |      |
| 3   | 數量     | `ProductItem/Quantity`    | int/float |      |
| 4   | 單價     | `ProductItem/UnitPrice`   | float     |      |
| 5   | 金額     | `ProductItem/Amount`      | int       |      |
| 6‑8 | (保留)   | –                         | –         | 留空 |

---

## 6. 非功能性需求 (NFR)

| 編號      | 類別     | 內容                                                    |
| --------- | -------- | ------------------------------------------------------- |
| **NFR‑P** | 效能     | 1000  檔  ≤ 60 s；記憶體峰值  < 500 MB                  |
| **NFR‑R** | 可靠性   | 單元測試覆蓋 ≥ 90%；關鍵函式需含型別檢查                |
| **NFR‑L** | 可維護性 | 程式碼遵循 PEP 8；函式說明使用 Google‑style docstring   |
| **NFR‑O** | 操作性   | 支援 `--log-level` (default INFO)；日誌以 rot‑file 輸出 |
| **NFR‑S** | 安全     | 僅讀取指定 input 目錄；遇到路徑 traversal 自動拒絕      |

---

## 7. CLI 參數一覽

| 參數             | 必填 | 預設        | 說明                               |
| ---------------- | ---- | ----------- | ---------------------------------- |
| `--input, -i`    | 否   | `./`        | XML 來源目錄                       |
| `--output, -o`   | 否   | `./`        | CSV 輸出目錄                       |
| `--seller-phone` | 否   | 空          | 賣方電話；空字串則 CSV 第 5 欄留空 |
| `--encoding`     | 否   | `utf-8-sig` | 輸出 CSV 編碼                      |
| `--skip-invalid` | 否   | `false`     | 跳過格式錯誤檔案                   |
| `--log-level`    | 否   | `INFO`      | DEBUG / INFO / WARNING / ERROR     |
| `--version`      | 否   | –           | 顯示版本並離開                     |

Exit code：0=成功；1=解析失敗；2=賣方不一致。

---

## 8. 驗收準則 (Acceptance Criteria)

1. 以附件兩檔 XML 執行轉換，輸出須與範例 `A0401_合併_PD88444028～PD88444029_V4.csv` 完全相符。
2. 程式於賣方不一致時應中斷並顯示 `SellerMismatchError`。
3. 啟用 `--skip-invalid` 時，應略過無效檔案且在 log 顯示 Warning。

---

## 9. 測試案例 (Test Cases)

| TC    | 目的         | 測試步驟            | 預期結果                    |
| ----- | ------------ | ------------------- | --------------------------- |
| TC‑01 | 正常轉換     | 兩檔 XML            | 產生 1×H、2×M、7×D 共 10 行 |
| TC‑02 | 賣方不一致   | 混入不同 Seller XML | exit code 2                 |
| TC‑03 | skip‑invalid | XML+破損檔          | 程式完成；log 含 Warning    |

---

## 10. TODO / 開放議題

-   eGUI 需求？
-   跨平台發行：PyPI or Stand‑alone exe？
-   稅率小數 → 百分比四捨五入規則是否固定？

---
