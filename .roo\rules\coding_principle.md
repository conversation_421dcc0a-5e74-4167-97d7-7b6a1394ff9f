# Coding Principles

-   ALWAYS write clean, readable and modular code
-   Keep files small and focused (<500 lines)
-   Test after every meaningful change.
-   For new feature development, strictly follow Test Driven Development(TDD): write tests first, then the code, then run the tests and update the code until tests pass
-   Follow the DRY principle to avoid redundancy and duplicate code
-   Use clear, consistent naming
-   Think thoroughly before coding. Write 2-3 reasoning paragraphs.
-   Write code that takes into account the different environments: dev, test, and prod
-   You are careful to only make changes that are requested
-   Mocking data is only needed for tests, never mock data for dev or prod
-   Never add stubbing or fake data patterns to code that affects the dev or prod environments
-   Never overwrite my .env file without first asking and confirming
-   每個函數都要嚴格定義 signature type hints 和 response type hints, 如果是複雜的 dictionary 回傳值, 使用 TypedDict 等資料模型

# Coding workflow preferences

-   Focus on the areas of code relevant to the task
-   Do not touch code that is unrelated to the task
-   Write thorough tests for all major functionality
-   Avoid making major changes to the patterns and architecture of how a feature works, after it has shown to work well, unless explicitly instructed
-   Always think about what other methods and areas of code might be affected by code changes

# Error Fixing

-   DO NOT JUMP TO CONCLUSIONS! Consider multiple possible causes before deciding.
-   When fixing an issue or bug, do not introduce a new pattern or technology without first exhausting all options for the existing implementation. And if you finally do this, make sure to remove the old implementation afterwards so we don’t have duplicate logic.
-   Make minimal necessary changes, changing as few lines of code as possible
-   in case of strange errors, ask the user to perform a Perplexity web search to find the latest up-to-date information

# Building Process

-   Verify each new feature works by telling the user how to test it
-   DO NOT write complicated and confusing code. Opt for the simple & modular approach.
-   when not sure what to do, tell the user to perform a web search

# Comments

-   ALWAYS try to add more helpful and explanatory comments into our code
-   NEVER delete old comments - unless they are obviously wrong / obsolete
-   Include LOTS of explanatory comments in your code. ALWAYS write well-documented code.
-   Document all changes and their reasoning IN THE COMMENTS YOU WRITE
-   when writing comments, use clear and easy-to-understand language and write in short sentences.

## 📌 Naming Conventions

-   模組資料夾與檔名：皆使用 snake_case
-   類別名稱：使用 PascalCase
-   函數名稱與變數名稱：使用 snake_case
-   Interface 類別：以 I 開頭結尾(e.g. IUserRepository)
